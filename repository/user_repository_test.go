package repository

import (
	"felix1234567890/go-trello/models"
	"felix1234567890/go-trello/utils"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupUserTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	if err := db.AutoMigrate(&models.User{}); err != nil {
		panic("failed to migrate database")
	}
	return db
}

func TestUserRepository_GetUsers(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	t.Run("should return empty slice if no users", func(t *testing.T) {
		users, err := repo.GetUsers()
		assert.NoError(t, err)
		assert.Empty(t, users)
	})

	t.Run("should return users if present", func(t *testing.T) {
		expectedUser := models.User{
			Username: "Test User",
			Email:    "<EMAIL>",
			Password: "password", // Will be hashed by CreateUser
		}
		_, err := repo.CreateUser(&expectedUser) // Use CreateUser to handle hashing
		assert.NoError(t, err)

		users, err := repo.GetUsers()
		assert.NoError(t, err)
		assert.Len(t, users, 1)
		assert.Equal(t, "Test User", users[0].Username)
		assert.Equal(t, "<EMAIL>", users[0].Email)
		// Password will be hashed, so we don't compare directly
	})
}

func TestUserRepository_GetUserById(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	userToCreate := models.User{
		Username: "Find User",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	createdUserID, err := repo.CreateUser(&userToCreate)
	assert.NoError(t, err)

	t.Run("should return user if found", func(t *testing.T) {
		foundUser, err := repo.GetUserById(strconv.FormatUint(uint64(createdUserID), 10))
		assert.NoError(t, err)
		assert.Equal(t, "Find User", foundUser.Username)
		assert.Equal(t, "<EMAIL>", foundUser.Email)
	})

	t.Run("should return error if user not found", func(t *testing.T) {
		_, err := repo.GetUserById("999") // Non-existent ID
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})
}

func TestUserRepository_DeleteUser(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	userToCreate := models.User{
		Username: "Delete User",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	createdUserID, err := repo.CreateUser(&userToCreate)
	assert.NoError(t, err)

	t.Run("should delete user successfully", func(t *testing.T) {
		err := repo.DeleteUser(strconv.FormatUint(uint64(createdUserID), 10))
		assert.NoError(t, err)

		_, err = repo.GetUserById(strconv.FormatUint(uint64(createdUserID), 10))
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound) // Should not find after deletion
	})

	t.Run("should return error if user not found for deletion", func(t *testing.T) {
		err := repo.DeleteUser("999") // Non-existent ID
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})
}

func TestUserRepository_UpdateUser(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	userToCreate := models.User{
		Username: "Original Name",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	createdUserID, err := repo.CreateUser(&userToCreate)
	assert.NoError(t, err)

	t.Run("should update user successfully", func(t *testing.T) {
		updateReq := &models.UpdateUserRequest{
			Username: "Updated Name",
			Email:    "<EMAIL>",
		}
		err := repo.UpdateUser(strconv.FormatUint(uint64(createdUserID), 10), updateReq)
		assert.NoError(t, err)

		updatedUser, err := repo.GetUserById(strconv.FormatUint(uint64(createdUserID), 10))
		assert.NoError(t, err)
		assert.Equal(t, "Updated Name", updatedUser.Username)
		assert.Equal(t, "<EMAIL>", updatedUser.Email)
		// Password should remain unchanged
	})

	t.Run("should return error if user not found for update", func(t *testing.T) {
		updateReq := &models.UpdateUserRequest{
			Username: "Non Existent",
		}
		err := repo.UpdateUser("999", updateReq) // Non-existent ID
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})
}

func TestUserRepository_CreateUser(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	t.Run("should create user successfully with hashed password", func(t *testing.T) {
		userToCreate := models.User{
			Username: "New User",
			Email:    "<EMAIL>",
			Password: "securepassword",
		}
		createdUserID, err := repo.CreateUser(&userToCreate)
		assert.NoError(t, err)
		assert.NotZero(t, createdUserID) // ID should be assigned

		// Verify user in DB and password is hashed
		foundUser, err := repo.GetUserById(strconv.FormatUint(uint64(createdUserID), 10))
		assert.NoError(t, err)
		assert.Equal(t, "New User", foundUser.Username)
		assert.Equal(t, "<EMAIL>", foundUser.Email)
		assert.True(t, utils.CheckPasswordHash("securepassword", foundUser.Password) == nil) // Check hashed password
	})

	t.Run("should return error for duplicate email", func(t *testing.T) {
		user1 := models.User{
			Username: "User One",
			Email:    "<EMAIL>",
			Password: "password",
		}
		_, err := repo.CreateUser(&user1)
		assert.NoError(t, err)

		user2 := models.User{
			Username: "User Two",
			Email:    "<EMAIL>", // Same email
			Password: "password",
		}
		_, err = repo.CreateUser(&user2)
		assert.Error(t, err) // Expect an error for duplicate email
	})
}

func TestUserRepository_LoginUser(t *testing.T) {
	db := setupUserTestDB()
	repo := NewUserRepository(db)

	plainPassword := "supersecret"
	userToCreate := models.User{
		Username: "Login User",
		Email:    "<EMAIL>",
		Password: plainPassword, // CreateUser will hash this
	}
	createdUserID, err := repo.CreateUser(&userToCreate)
	assert.NoError(t, err)

	t.Run("should successfully login with correct credentials", func(t *testing.T) {
		loginReq := &models.LoginUserRequest{
			Email:    "<EMAIL>",
			Password: plainPassword,
		}
		userID, err := repo.LoginUser(loginReq)
		assert.NoError(t, err)
		assert.Equal(t, createdUserID, userID)
	})

	t.Run("should return ErrInvalidCredentials for wrong password", func(t *testing.T) {
		loginReq := &models.LoginUserRequest{
			Email:    "<EMAIL>",
			Password: "wrongpassword",
		}
		_, err := repo.LoginUser(loginReq)
		assert.ErrorIs(t, err, utils.ErrInvalidCredentials)
	})

	t.Run("should return ErrInvalidCredentials for non-existent email", func(t *testing.T) {
		loginReq := &models.LoginUserRequest{
			Email:    "<EMAIL>",
			Password: "anypassword",
		}
		_, err := repo.LoginUser(loginReq)
		assert.ErrorIs(t, err, utils.ErrInvalidCredentials)
	})
}
