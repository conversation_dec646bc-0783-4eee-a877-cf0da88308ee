basePath: /
definitions:
  fiber.Map:
    additionalProperties: true
    type: object
  gorm.DeletedAt:
    properties:
      time:
        type: string
      valid:
        description: Valid is true if Time is not NULL
        type: boolean
    type: object
  models.CreateUserRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      username:
        minLength: 5
        type: string
    required:
    - email
    - password
    - username
    type: object
  models.LoginUserRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  models.UpdateUserRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      username:
        minLength: 5
        type: string
    type: object
  models.User:
    properties:
      createdAt:
        type: string
      deletedAt:
        $ref: '#/definitions/gorm.DeletedAt'
      email:
        type: string
      id:
        type: integer
      password:
        type: string
      updatedAt:
        type: string
      username:
        type: string
    type: object
host: localhost:3000
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: This is a sample swagger for Fiber
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Go-Trello API
  version: "1.0"
paths:
  /login:
    post:
      consumes:
      - application/json
      description: Authenticate a user and return a token
      parameters:
      - description: Login credentials
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/models.LoginUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Authentication successful, token returned
          schema:
            $ref: '#/definitions/fiber.Map'
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: User login
      tags:
      - users
  /me:
    get:
      consumes:
      - application/json
      description: Get details of the currently authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: Details of the authenticated user
          schema:
            $ref: '#/definitions/fiber.Map'
      security:
      - ApiKeyAuth: []
      summary: Get current user
      tags:
      - users
  /users:
    get:
      consumes:
      - application/json
      description: Get a list of all users
      produces:
      - application/json
      responses:
        "200":
          description: List of users
          schema:
            items:
              $ref: '#/definitions/models.User'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get all users
      tags:
      - users
    post:
      consumes:
      - application/json
      description: Create a new user and return an authentication token
      parameters:
      - description: User details
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully, token returned
          schema:
            $ref: '#/definitions/fiber.Map'
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Create a new user
      tags:
      - users
  /users/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a user by ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User deleted successfully
          schema:
            $ref: '#/definitions/fiber.Map'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Delete a user
      tags:
      - users
    get:
      consumes:
      - application/json
      description: Get details of a specific user
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User details
          schema:
            $ref: '#/definitions/models.User'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Get a user by ID
      tags:
      - users
    put:
      consumes:
      - application/json
      description: Update a user's details
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User details to update
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            $ref: '#/definitions/fiber.Map'
        "400":
          description: Invalid request body or validation errors
          schema:
            $ref: '#/definitions/fiber.Map'
        "404":
          description: User not found
          schema:
            $ref: '#/definitions/fiber.Map'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fiber.Map'
      summary: Update a user
      tags:
      - users
swagger: "2.0"
