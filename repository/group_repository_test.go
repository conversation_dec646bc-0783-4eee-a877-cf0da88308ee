package repository

import (
	"felix1234567890/go-trello/models"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupGroupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	db.AutoMigrate(&models.User{}, &models.Group{})
	return db
}

func TestGroupCRUD(t *testing.T) {
	db := setupGroupTestDB()
	repo := NewGroupRepository(db)
	// Create
	group := &models.Group{Name: "TestGroup"}
	err := repo.CreateGroup(group)
	assert.NoError(t, err)
	assert.NotZero(t, group.ID)
	// Read
	g, err := repo.GetGroupByID(group.ID)
	assert.NoError(t, err)
	assert.Equal(t, "TestGroup", g.Name)
	// Update
	group.Name = "UpdatedGroup"
	err = repo.UpdateGroup(group)
	assert.NoError(t, err)
	g, _ = repo.GetGroupByID(group.ID)
	assert.Equal(t, "UpdatedGroup", g.Name)
	// Delete
	err = repo.DeleteGroup(group.ID)
	assert.NoError(t, err)
	_, err = repo.GetGroupByID(group.ID)
	assert.Error(t, err)
}

func TestAddAndRemoveUserToGroup(t *testing.T) {
	db := setupGroupTestDB()
	repo := NewGroupRepository(db)
	user := &models.User{Username: "user1", Email: "<EMAIL>", Password: "pass"}
	group := &models.Group{Name: "Group1"}
	db.Create(user)
	repo.CreateGroup(group)
	// Add user to group
	err := repo.AddUserToGroup(group.ID, user.ID)
	assert.NoError(t, err)
	g, _ := repo.GetGroupByID(group.ID)
	assert.Equal(t, 1, len(g.Users))
	assert.Equal(t, user.ID, g.Users[0].ID)
	// Remove user from group
	err = repo.RemoveUserFromGroup(group.ID, user.ID)
	assert.NoError(t, err)
	g, _ = repo.GetGroupByID(group.ID)
	assert.Equal(t, 0, len(g.Users))
}

func TestGetAllGroups(t *testing.T) {
	db := setupGroupTestDB()
	repo := NewGroupRepository(db)

	// Create some groups
	group1 := &models.Group{Name: "Group1"}
	repo.CreateGroup(group1)
	group2 := &models.Group{Name: "Group2"}
	repo.CreateGroup(group2)

	groups, err := repo.GetAllGroups()
	assert.NoError(t, err)
	assert.Len(t, groups, 2)

	// Verify group names (order might not be guaranteed, so check for presence)
	foundGroup1 := false
	foundGroup2 := false
	for _, g := range groups {
		if g.Name == "Group1" {
			foundGroup1 = true
		}
		if g.Name == "Group2" {
			foundGroup2 = true
		}
	}
	assert.True(t, foundGroup1, "Group1 not found")
	assert.True(t, foundGroup2, "Group2 not found")
}

func TestCountGroupsLedByUser(t *testing.T) {
	db := setupGroupTestDB()
	repo := NewGroupRepository(db)

	user1 := &models.User{Username: "leader1", Email: "<EMAIL>", Password: "pass"}
	db.Create(user1)

	user2 := &models.User{Username: "leader2", Email: "<EMAIL>", Password: "pass"}
	db.Create(user2)

	// User1 leads 2 groups
	repo.CreateGroup(&models.Group{Name: "LedGroup1ByUser1", LeaderID: user1.ID})
	repo.CreateGroup(&models.Group{Name: "LedGroup2ByUser1", LeaderID: user1.ID})

	// User2 leads 1 group
	repo.CreateGroup(&models.Group{Name: "LedGroup1ByUser2", LeaderID: user2.ID})

	countUser1, err := repo.CountGroupsLedByUser(user1.ID)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), countUser1)

	countUser2, err := repo.CountGroupsLedByUser(user2.ID)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), countUser2)

	countNonLeader, err := repo.CountGroupsLedByUser(999) // Non-existent user or user with no groups
	assert.NoError(t, err)
	assert.Equal(t, int64(0), countNonLeader)
}

func TestGroupLeaderConstraints(t *testing.T) {
	db := setupGroupTestDB()
	repo := NewGroupRepository(db)

	leaderUser := &models.User{Username: "testleader", Email: "<EMAIL>", Password: "pass"}
	db.Create(leaderUser)

	// Create 3 groups led by leaderUser - should be fine
	for i := 0; i < 3; i++ {
		group := &models.Group{Name: "LeaderGroup" + string(rune(i)), LeaderID: leaderUser.ID}
		err := repo.CreateGroup(group)
		assert.NoError(t, err)
	}

	// Try to create a 4th group led by leaderUser - should fail
	fourthGroup := &models.Group{Name: "FourthLeaderGroup", LeaderID: leaderUser.ID}
	err := repo.CreateGroup(fourthGroup)
	assert.Error(t, err)
	assert.Equal(t, "user cannot be a leader in more than 3 groups", err.Error())

	// Create a group without a leader, then update it to have leaderUser as leader (who already leads 3)
	otherUser := &models.User{Username: "otheruser", Email: "<EMAIL>", Password: "pass"}
	db.Create(otherUser)

	groupToUpdate := &models.Group{Name: "GroupToUpdateLeader"}
	err = repo.CreateGroup(groupToUpdate)
	assert.NoError(t, err)

	// Try to update groupToUpdate to be led by leaderUser (who already leads 3 groups)
	groupToUpdate.LeaderID = leaderUser.ID
	err = repo.UpdateGroup(groupToUpdate)
	assert.Error(t, err)
	assert.Equal(t, "user cannot be a leader in more than 3 groups", err.Error())

	// Update one of the leaderUser's groups to a different leader
	var firstLedGroup models.Group
	db.Where("leader_id = ?", leaderUser.ID).First(&firstLedGroup)
	firstLedGroup.LeaderID = otherUser.ID
	err = repo.UpdateGroup(&firstLedGroup)
	assert.NoError(t, err)

	// Now leaderUser leads 2 groups, try to make groupToUpdate led by leaderUser again - should succeed
	groupToUpdate.LeaderID = leaderUser.ID // groupToUpdate still has its original ID and no leader
	// We need to fetch it again to ensure we have the latest state before updating, or use the one we created
	var freshGroupToUpdate models.Group
	db.First(&freshGroupToUpdate, groupToUpdate.ID)
	freshGroupToUpdate.LeaderID = leaderUser.ID
	err = repo.UpdateGroup(&freshGroupToUpdate)
	assert.NoError(t, err)

	count, _ := repo.CountGroupsLedByUser(leaderUser.ID)
	assert.Equal(t, int64(3), count) // leaderUser should now lead 3 groups again
}
