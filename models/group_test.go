package models

import (
	"encoding/json"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	db.AutoMigrate(&User{}, &Group{})
	return db
}

func TestGroupModel(t *testing.T) {
	t.Run("should create group with valid data", func(t *testing.T) {
		db := setupTestDB()
		
		// Create a leader user
		leader := &User{Username: "leader", Email: "<EMAIL>", Password: "password"}
		db.Create(leader)
		
		// Create group
		group := &Group{
			Name:     "Test Group",
			LeaderID: leader.ID,
		}
		
		result := db.Create(group)
		assert.NoError(t, result.Error)
		assert.NotZero(t, group.ID)
		assert.Equal(t, "Test Group", group.Name)
		assert.Equal(t, leader.ID, group.LeaderID)
	})
	
	t.Run("should enforce unique name constraint", func(t *testing.T) {
		db := setupTestDB()
		
		// Create first group
		group1 := &Group{Name: "Duplicate Group"}
		db.Create(group1)
		
		// Try to create second group with same name
		group2 := &Group{Name: "Duplicate Group"}
		result := db.Create(group2)
		assert.Error(t, result.Error)
	})
	
	t.Run("should handle many-to-many relationship with users", func(t *testing.T) {
		db := setupTestDB()
		
		// Create users
		user1 := &User{Username: "user1", Email: "<EMAIL>", Password: "password"}
		user2 := &User{Username: "user2", Email: "<EMAIL>", Password: "password"}
		db.Create(user1)
		db.Create(user2)
		
		// Create group
		group := &Group{Name: "Test Group"}
		db.Create(group)
		
		// Add users to group
		db.Model(&group).Association("Users").Append(user1, user2)
		
		// Verify relationship
		var groupWithUsers Group
		db.Preload("Users").First(&groupWithUsers, group.ID)
		assert.Len(t, groupWithUsers.Users, 2)
		assert.Equal(t, user1.ID, groupWithUsers.Users[0].ID)
		assert.Equal(t, user2.ID, groupWithUsers.Users[1].ID)
	})
	
	t.Run("should handle leader relationship", func(t *testing.T) {
		db := setupTestDB()
		
		// Create leader
		leader := &User{Username: "leader", Email: "<EMAIL>", Password: "password"}
		db.Create(leader)
		
		// Create group with leader
		group := &Group{Name: "Test Group", LeaderID: leader.ID}
		db.Create(group)
		
		// Load group with leader
		var groupWithLeader Group
		db.Preload("Leader").First(&groupWithLeader, group.ID)
		assert.NotNil(t, groupWithLeader.Leader)
		assert.Equal(t, leader.ID, groupWithLeader.Leader.ID)
		assert.Equal(t, leader.Username, groupWithLeader.Leader.Username)
	})
}

func TestGroupJSONSerialization(t *testing.T) {
	t.Run("should serialize group to JSON", func(t *testing.T) {
		group := &Group{
			Name: "Test Group",
			LeaderID: 1,
		}
		group.ID = 1
		
		jsonData, err := json.Marshal(group)
		assert.NoError(t, err)
		
		var result map[string]interface{}
		json.Unmarshal(jsonData, &result)
		
		assert.Equal(t, "Test Group", result["name"])
		// LeaderID field doesn't have json tag, so it uses default field name
		assert.Equal(t, float64(1), result["LeaderID"])
	})
	
	t.Run("should deserialize group from JSON", func(t *testing.T) {
		// Use the actual field name since LeaderID doesn't have a json tag
		jsonStr := `{"name":"Test Group","LeaderID":1}`
		
		var group Group
		err := json.Unmarshal([]byte(jsonStr), &group)
		assert.NoError(t, err)
		assert.Equal(t, "Test Group", group.Name)
		assert.Equal(t, uint(1), group.LeaderID)
	})
}

func TestCreateGroupRequest(t *testing.T) {
	validator := validator.New()
	
	t.Run("should validate valid request", func(t *testing.T) {
		req := CreateGroupRequest{
			Name:     "Valid Group",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.NoError(t, err)
	})
	
	t.Run("should reject empty name", func(t *testing.T) {
		req := CreateGroupRequest{
			Name:     "",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "required")
	})
	
	t.Run("should reject short name", func(t *testing.T) {
		req := CreateGroupRequest{
			Name:     "AB",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min")
	})
	
	t.Run("should accept minimum length name", func(t *testing.T) {
		req := CreateGroupRequest{
			Name:     "ABC",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.NoError(t, err)
	})
	
	t.Run("should serialize to JSON", func(t *testing.T) {
		req := CreateGroupRequest{
			Name:     "Test Group",
			LeaderID: 1,
		}
		
		jsonData, err := json.Marshal(req)
		assert.NoError(t, err)
		
		var result map[string]interface{}
		json.Unmarshal(jsonData, &result)
		
		assert.Equal(t, "Test Group", result["name"])
		assert.Equal(t, float64(1), result["leader_id"])
	})
	
	t.Run("should deserialize from JSON", func(t *testing.T) {
		jsonStr := `{"name":"Test Group","leader_id":1}`
		
		var req CreateGroupRequest
		err := json.Unmarshal([]byte(jsonStr), &req)
		assert.NoError(t, err)
		assert.Equal(t, "Test Group", req.Name)
		assert.Equal(t, uint(1), req.LeaderID)
	})
}

func TestUpdateGroupRequest(t *testing.T) {
	validator := validator.New()
	
	t.Run("should validate valid request", func(t *testing.T) {
		req := UpdateGroupRequest{
			Name:     "Updated Group",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.NoError(t, err)
	})
	
	t.Run("should allow empty name (omitempty)", func(t *testing.T) {
		req := UpdateGroupRequest{
			Name:     "",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.NoError(t, err)
	})
	
	t.Run("should reject short name when provided", func(t *testing.T) {
		req := UpdateGroupRequest{
			Name:     "AB",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min")
	})
	
	t.Run("should accept minimum length name when provided", func(t *testing.T) {
		req := UpdateGroupRequest{
			Name:     "ABC",
			LeaderID: 1,
		}
		
		err := validator.Struct(req)
		assert.NoError(t, err)
	})
	
	t.Run("should serialize to JSON", func(t *testing.T) {
		req := UpdateGroupRequest{
			Name:     "Updated Group",
			LeaderID: 2,
		}
		
		jsonData, err := json.Marshal(req)
		assert.NoError(t, err)
		
		var result map[string]interface{}
		json.Unmarshal(jsonData, &result)
		
		assert.Equal(t, "Updated Group", result["name"])
		assert.Equal(t, float64(2), result["leader_id"])
	})
	
	t.Run("should deserialize from JSON", func(t *testing.T) {
		jsonStr := `{"name":"Updated Group","leader_id":2}`
		
		var req UpdateGroupRequest
		err := json.Unmarshal([]byte(jsonStr), &req)
		assert.NoError(t, err)
		assert.Equal(t, "Updated Group", req.Name)
		assert.Equal(t, uint(2), req.LeaderID)
	})
}

func TestGroupStructFields(t *testing.T) {
	t.Run("should have correct struct tags", func(t *testing.T) {
		group := Group{}
		
		// Test that the struct has the expected fields
		assert.IsType(t, "", group.Name)
		assert.IsType(t, []*User{}, group.Users)
		assert.IsType(t, uint(0), group.LeaderID)
		assert.IsType(t, (*User)(nil), group.Leader)
	})
	
	t.Run("should initialize with zero values", func(t *testing.T) {
		group := Group{}
		
		assert.Equal(t, "", group.Name)
		assert.Nil(t, group.Users)
		assert.Equal(t, uint(0), group.LeaderID)
		assert.Nil(t, group.Leader)
	})
}

func TestGroupRequestStructFields(t *testing.T) {
	t.Run("CreateGroupRequest should have correct fields", func(t *testing.T) {
		req := CreateGroupRequest{}
		
		assert.IsType(t, "", req.Name)
		assert.IsType(t, uint(0), req.LeaderID)
		assert.Equal(t, "", req.Name)
		assert.Equal(t, uint(0), req.LeaderID)
	})
	
	t.Run("UpdateGroupRequest should have correct fields", func(t *testing.T) {
		req := UpdateGroupRequest{}
		
		assert.IsType(t, "", req.Name)
		assert.IsType(t, uint(0), req.LeaderID)
		assert.Equal(t, "", req.Name)
		assert.Equal(t, uint(0), req.LeaderID)
	})
}
