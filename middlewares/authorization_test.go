package middlewares

import (
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"felix1234567890/go-trello/models"
	"felix1234567890/go-trello/utils"
)

func setupTestDB() *gorm.DB {
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	db.AutoMigrate(&models.User{})
	return db
}

func generateToken(userID uint) string {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id": userID,
	})
	tokenString, _ := token.SignedString([]byte(utils.SECRET_KEY))
	return tokenString
}

func TestDeserializeUser_ValidToken(t *testing.T) {
	db := setupTestDB()
	user := models.User{Username: "testuser", Email: "<EMAIL>"}
	db.Create(&user)
	token := generateToken(user.ID)

	app := fiber.New()
	app.Use(DeserializeUser(db))
	app.Get("/protected", func(c *fiber.Ctx) error {
		u := c.Locals("user").(models.User)
		return c.JSON(fiber.Map{"id": u.ID, "username": u.Username})
	})

	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	resp, _ := app.Test(req)
	assert.Equal(t, fiber.StatusOK, resp.StatusCode)
}

func TestDeserializeUser_MissingToken(t *testing.T) {
	db := setupTestDB()
	app := fiber.New()
	app.Use(DeserializeUser(db))
	app.Get("/protected", func(c *fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	req := httptest.NewRequest("GET", "/protected", nil)
	resp, _ := app.Test(req)
	assert.Equal(t, fiber.StatusUnauthorized, resp.StatusCode)
}

func TestDeserializeUser_InvalidToken(t *testing.T) {
	db := setupTestDB()
	app := fiber.New()
	app.Use(DeserializeUser(db))
	app.Get("/protected", func(c *fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer invalidtoken")
	resp, _ := app.Test(req)
	assert.Equal(t, fiber.StatusUnauthorized, resp.StatusCode)
}

func TestDeserializeUser_NonExistentUser(t *testing.T) {
	db := setupTestDB()
	// Don't create user in DB
	token := generateToken(9999)

	app := fiber.New()
	app.Use(DeserializeUser(db))
	app.Get("/protected", func(c *fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	req := httptest.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	resp, _ := app.Test(req)
	assert.Equal(t, fiber.StatusForbidden, resp.StatusCode)
}
