package models

import "gorm.io/gorm"

// Group represents a group with a unique name
// and a many-to-many relationship with users.
type Group struct {
	gorm.Model
	Name     string  `json:"name" gorm:"unique;not null"`
	Users    []*User `json:"users" gorm:"many2many:user_groups;"`
	LeaderID uint
	Leader   *User `json:"leader"`
}

type CreateGroupRequest struct {
	Name     string `json:"name" validate:"required,min=3"`
	LeaderID uint   `json:"leader_id"`
}

type UpdateGroupRequest struct {
	Name     string `json:"name" validate:"omitempty,min=3"`
	LeaderID uint   `json:"leader_id"`
}
